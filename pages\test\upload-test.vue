<template>
  <view class="container">
    <view class="test-section">
      <text class="title">上传接口测试</text>
      
      <view class="test-item">
        <text class="label">当前baseURL:</text>
        <text class="value">{{ baseURL }}</text>
      </view>
      
      <view class="test-item">
        <text class="label">Authorization:</text>
        <text class="value">{{ authorization ? '已设置' : '未设置' }}</text>
      </view>
      
      <button class="test-btn" @click="testUploadPaths">测试上传路径</button>
      
      <view class="results">
        <text class="results-title">测试结果:</text>
        <view v-for="(result, index) in testResults" :key="index" class="result-item">
          <text :class="['result-text', result.success ? 'success' : 'error']">
            {{ result.url }}: {{ result.message }}
          </text>
        </view>
      </view>
      
      <button class="test-btn" @click="testImageUpload">测试图片上传</button>
      
      <view v-if="uploadResult" class="upload-result">
        <text class="results-title">上传结果:</text>
        <text :class="['result-text', uploadResult.success ? 'success' : 'error']">
          {{ uploadResult.message }}
        </text>
        <view v-if="uploadResult.data">
          <text class="label">返回数据:</text>
          <text class="value">{{ JSON.stringify(uploadResult.data, null, 2) }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue';
import request from '../../api/request';

const baseURL = request.BASE_URL;
const authorization = uni.getStorageSync('authorization');
const testResults = ref([]);
const uploadResult = ref(null);

// 测试不同的上传路径
const testUploadPaths = async () => {
  testResults.value = [];
  
  const paths = [
    'daily/upload',
    'upload',
    'api/daily/upload',
    'file/upload'
  ];
  
  for (const path of paths) {
    try {
      const response = await uni.request({
        url: baseURL + path,
        method: 'GET',
        header: {
          'Authorization': authorization
        }
      });
      
      testResults.value.push({
        url: path,
        success: response.statusCode !== 404,
        message: `状态码: ${response.statusCode}`
      });
    } catch (error) {
      testResults.value.push({
        url: path,
        success: false,
        message: `错误: ${error.message}`
      });
    }
  }
};

// 测试图片上传
const testImageUpload = async () => {
  try {
    uploadResult.value = null;
    
    const chooseRes = await uni.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera']
    });
    
    if (!chooseRes.tempFilePaths || chooseRes.tempFilePaths.length === 0) {
      throw new Error('未选择图片');
    }
    
    const tempFilePath = chooseRes.tempFilePaths[0];
    
    uni.showLoading({
      title: '测试上传中...',
      mask: true
    });
    
    // 测试第一个路径
    const uploadRes = await uni.uploadFile({
      url: baseURL + 'daily/upload',
      filePath: tempFilePath,
      name: 'file',
      header: {
        'Authorization': authorization
      }
    });
    
    uni.hideLoading();
    
    let result;
    try {
      result = JSON.parse(uploadRes.data);
    } catch (e) {
      result = { raw: uploadRes.data };
    }
    
    uploadResult.value = {
      success: uploadRes.statusCode === 200,
      message: `状态码: ${uploadRes.statusCode}`,
      data: result
    };
    
  } catch (error) {
    uni.hideLoading();
    uploadResult.value = {
      success: false,
      message: error.message,
      data: null
    };
  }
};
</script>

<style>
.container {
  padding: 20rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}

.test-section {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  display: block;
  text-align: center;
}

.test-item {
  display: flex;
  margin-bottom: 20rpx;
  align-items: center;
}

.label {
  font-size: 28rpx;
  color: #666;
  width: 200rpx;
}

.value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  word-break: break-all;
}

.test-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #007AFF;
  color: white;
  border-radius: 8rpx;
  font-size: 28rpx;
  text-align: center;
  border: none;
  margin: 20rpx 0;
}

.results {
  margin-top: 30rpx;
}

.results-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.result-item {
  margin-bottom: 10rpx;
}

.result-text {
  font-size: 26rpx;
  padding: 10rpx;
  border-radius: 4rpx;
  display: block;
}

.success {
  background-color: #e8f5e8;
  color: #4CAF50;
}

.error {
  background-color: #ffeaea;
  color: #f44336;
}

.upload-result {
  margin-top: 30rpx;
  padding: 20rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
}
</style>
