// 在你的 UserController 中，修改 uploadAvatar 方法，添加更多调试信息：

/**
 * 上传头像文件
 */
@PostMapping("/upload-avatar")
@UserLogin
@CrossOrigin
public CommonResult<String> uploadAvatar(@RequestParam("file") MultipartFile file, User userInfo) {
    try {
        System.out.println("=== 头像上传调试信息 ===");
        System.out.println("收到头像上传请求");
        System.out.println("用户ID: " + userInfo.getId());
        System.out.println("文件是否为空: " + (file == null ? "null" : file.isEmpty()));
        
        if (file != null) {
            System.out.println("文件名: " + file.getOriginalFilename());
            System.out.println("文件大小: " + file.getSize());
            System.out.println("文件类型: " + file.getContentType());
        }

        // 1. 文件校验
        if (file == null || file.isEmpty()) {
            System.err.println("文件为空或null");
            return CommonResult.webError(-1, "文件不能为空");
        }

        // 2. 文件类型校验
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || !isImageFile(originalFilename)) {
            System.err.println("文件类型不支持: " + originalFilename);
            return CommonResult.webError(-1, "只支持图片格式文件");
        }

        // 3. 文件大小校验（限制5MB）
        if (file.getSize() > 5 * 1024 * 1024) {
            System.err.println("文件过大: " + file.getSize());
            return CommonResult.webError(-1, "文件大小不能超过5MB");
        }

        // 4. 生成文件路径：avatar/用户ID/时间戳.扩展名
        String fileExtension = getFileExtension(originalFilename);
        String fileName = userInfo.getId() + "/" + System.currentTimeMillis() + "." + fileExtension;
        System.out.println("生成的文件名: " + fileName);

        // 5. 上传到OSS
        boolean uploadResult = aliyunService.uploadAvatarFile(file, fileName);
        System.out.println("OSS上传结果: " + uploadResult);

        if (uploadResult) {
            // 返回包含 avatar/ 前缀的文件路径
            String filePath = "avatar/" + fileName;
            System.out.println("头像上传成功，返回路径: " + filePath);
            return CommonResult.webSuccess("上传成功", filePath);
        } else {
            System.err.println("OSS上传失败");
            return CommonResult.webError(-1, "上传失败");
        }
    } catch (Exception e) {
        System.err.println("头像上传异常: " + e.getMessage());
        e.printStackTrace();
        return CommonResult.webError(-1, "上传失败: " + e.getMessage());
    }
}

// 同时，你可以添加一个测试接口来验证路由是否正确：

/**
 * 测试头像上传接口是否可达
 */
@GetMapping("/test-upload")
@UserLogin
@CrossOrigin
public CommonResult<String> testUpload(User userInfo) {
    return CommonResult.webSuccess("头像上传接口可达，用户ID: " + userInfo.getId());
}
