
.container {
	display: flex;
	flex-direction: column;
	height: 100vh;
	background-color: #f5f5f5;
}
.header {
	padding: 0.625rem;
	display: flex;
	align-items: center;
	justify-content: space-between;
	background-color: #fff;
	border-bottom: 0.03125rem solid #f0f0f0;
	position: -webkit-sticky;
	position: sticky;
	top: 0;
	z-index: 100;
}
.logo-area {
	display: flex;
	align-items: center;
}
.logo {
	width: 1.875rem;
	height: 1.875rem;
	margin-right: 0.3125rem;
}
.title {
	font-size: 1.125rem;
	font-weight: bold;
	color: #333;
}
.search-box {
	flex: 1;
	margin-left: 0.625rem;
	height: 2.1875rem;
	background-color: #f5f5f5;
	border-radius: 1.09375rem;
	display: flex;
	align-items: center;
	padding: 0 0.625rem;
	position: relative;
}
.search-input {
	flex: 1;
	height: 100%;
	font-size: 0.875rem;
	color: #333;
	background: transparent;
	border: none;
	outline: none;
}
.search-input::-webkit-input-placeholder {
	color: #999;
}
.search-input::placeholder {
	color: #999;
}
.search-icon {
	position: absolute;
	right: 0.625rem;
	color: #999;
	font-size: 0.75rem;
}
.clear-icon {
	position: absolute;
	right: 0.625rem;
	color: #999;
	font-size: 0.75rem;
	width: 0.9375rem;
	height: 0.9375rem;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #ddd;
	border-radius: 50%;
}
.waterfall {
	flex: 1;
	padding: 0.3125rem;
	padding-top: 0.5rem;
	box-sizing: border-box;
}
.waterfall-wrapper {
	display: flex;
	justify-content: space-between;
}
.waterfall-column {
	width: 49%;
}

/* 错开布局，左列稍微向上偏移 */
.left-column {
	margin-top: -0.625rem;
}
.waterfall-item {
	border-radius: 0.375rem;
	background-color: #fff;
	overflow: hidden;
	box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
	position: relative;
}
.media-wrapper {
	position: relative;
	width: 100%;
	overflow: hidden;
}
.item-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
}
.video-icon {
	position: absolute;
	right: 0.625rem;
	bottom: 0.625rem;
	width: 1.875rem;
	height: 1.875rem;
	background-color: rgba(0, 0, 0, 0.5);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #fff;
}
.floating-title {
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	padding: 0.5rem;
	background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
	color: #fff;
	font-weight: bold;
	font-size: 0.875rem;
}
.item-content {
	padding: 0.5rem;
}
.item-title {
	font-size: 0.875rem;
	font-weight: bold;
	color: #333;
	margin-bottom: 0.25rem;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	overflow: hidden;
	text-overflow: ellipsis;
}
.item-desc {
	font-size: 0.75rem;
	color: #666;
	line-height: 1.5;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	overflow: hidden;
	text-overflow: ellipsis;
	margin-bottom: 0.5rem;
}
.item-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: 0.3125rem;
}
.user-info {
	display: flex;
	align-items: center;
}
.user-avatar {
	width: 1.25rem;
	height: 1.25rem;
	border-radius: 50%;
	margin-right: 0.3125rem;
}
.user-name {
	font-size: 0.6875rem;
	color: #999;
}
.like-info {
	display: flex;
	align-items: center;
}
.icon-rabbit{
	width: 0.75rem;
	height: 0.75rem;
}
.like-count {
	font-size: 0.6875rem;
	color: #999;
	margin-left: 0.125rem;
}
.loading {
	text-align: center;
	padding: 0.625rem 0;
}
.loading-text {
	font-size: 0.75rem;
	color: #999;
}
.no-more {
	text-align: center;
	padding: 0.625rem 0;
}
.no-more-text {
	font-size: 0.75rem;
	color: #999;
}

/* 添加图标字体类 */
.icon-play:before {
	content: "\e600";
}
.icon-like:before {
	content: "\e601";
}
.icon-search:before {
	content: "\e602";
}
