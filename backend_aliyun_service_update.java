// 你的 uploadAvatarFile 方法已经很好，只需要确保这样：

/**
 * 上传头像文件
 *
 * @param uploadFile 上传文件流
 * @param fileName   文件名/路径 (格式: "用户ID/时间戳.扩展名")
 * @return 是否上传成功
 */
public boolean uploadAvatarFile(MultipartFile uploadFile, String fileName) {
    if (uploadFile == null || fileName == null) {
        return false;
    }

    OSS ossClient = new OSSClientBuilder().build(
            ossConfig.getEndpoint(),
            ossConfig.getAccessKeyId(),
            ossConfig.getAccessKeySecret());

    try {
        InputStream inputStream = uploadFile.getInputStream();
        
        // 构建 OSS 对象名：avatar/用户ID/时间戳.扩展名
        String objectName;
        if (appConfig.isDebug()) {
            objectName = "debug/avatar/" + StringUtil.stripBefore(fileName, "/");
        } else {
            objectName = "avatar/" + StringUtil.stripBefore(fileName, "/");
        }

        System.out.println("OSS 头像对象名: " + objectName);

        ossClient.putObject(ossConfig.getBucketName(), objectName, inputStream);
        return true;
    } catch (IOException e) {
        System.err.println("头像文件上传失败: " + e.getMessage());
        e.printStackTrace();
        return false;
    } finally {
        ossClient.shutdown();
    }
}

// 同时确保 loadFileUrl 方法能正确处理头像路径：

public String loadFileUrl(String fileName) {
    if (Objects.isNull(fileName)) {
        return null;
    }

    fileName = fileName.replaceAll("\\r\\n|\\r|\\n", "");

    HttpUrl.Builder httpBuilder = Objects.requireNonNull(HttpUrl.parse(ossConfig.getDomain())).newBuilder();
    
    if (appConfig.isDebug()) {
        httpBuilder.addPathSegments("debug");
    }
    
    // 处理不同类型的文件路径
    if (fileName.startsWith("avatar/") || fileName.startsWith("daily/")) {
        // 如果已经包含文件夹前缀，直接使用
        httpBuilder.addPathSegments(StringUtil.stripBefore(fileName, "/"));
    } else {
        // 兼容旧的文件路径格式
        httpBuilder.addPathSegments(fileName);
    }
    
    return httpBuilder.build().toString();
}

// UserService.editUserInfo 方法保持不变，但确保正确处理 null 值：

public void editUserInfo(Long userId, String nickname, String avatar, String gender, String birthday) {
    // 先从数据库查询原始用户信息
    User originalUser = baseMapper.selectById(userId);
    if (originalUser == null) {
        throw new RuntimeException("用户不存在，无法进行信息更新");
    }

    User user = new User();
    user.setId(userId);

    // 只更新非空字段
    user.setNickname(nickname != null ? nickname : originalUser.getNickname());
    user.setAvatar(avatar != null ? avatar : originalUser.getAvatar());
    user.setBirthday(birthday != null ? birthday : originalUser.getBirthday());
    user.setGender(gender != null ? gender : originalUser.getGender());
    user.setUpdateTime(LocalDateTime.now());

    System.out.println("更新用户信息: " + user);
    
    int result = baseMapper.updateById(user);
    System.out.println("更新结果: " + result);
}
