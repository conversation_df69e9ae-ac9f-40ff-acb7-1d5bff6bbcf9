<template>
  <view class="container">
    <!-- 用户信息卡片 -->
    <view class="user-card">
      <view class="avatar-container">
        <image class="avatar" :src="getAvatarUrl() || '/static/user/avatar.jpg'"></image>
        <button class="edit-avatar-btn" @click="showAvatarPicker">更换头像</button>
      </view>

      <view class="user-info">
        <text class="nickname">{{ userInfo.nickname || '未设置昵称' }}</text>
        <text class="username">ID: {{ userInfo.id }}</text>
      </view>
    </view>

    <!-- 设置表单 -->
    <view class="settings-form">
      <view class="form-item">
        <text class="label">昵称</text>
        <input class="input" v-model="formData.nickname" placeholder="请输入昵称" />
      </view>

      <view class="form-item">
        <text class="label">性别</text>
        <picker class="picker" mode="selector" :range="genderOptions" @change="onGenderChange">
          <text>{{ formData.gender || '请选择性别' }}</text>
        </picker>
      </view>

      <view class="form-item">
        <text class="label">生日</text>
        <picker class="picker" mode="date" @change="onBirthdayChange">
          <text>{{ formData.birthday || '请选择生日' }}</text>
        </picker>
      </view>

      <button class="save-btn" @click="saveSettings">保存设置</button>
      <button class="logout-btn" @click="logout">退出登录</button>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import request from '../../../api/request';

const baseURL = request.BASE_URL;
const userInfo = ref({});
const formData = ref({
  nickname: '',
  gender: '',
  birthday: ''
});
const genderOptions = ['男', '女', '其他'];

// 头像相关状态
const selectedAvatarPath = ref(''); // 存储选择的头像本地路径

// 获取用户信息
const getUserInfo = async () => {
  try {
    const res = await uni.request({
      url: baseURL + 'user',
      method: 'GET',
      header: {
        'Cookie': `Authorization=${uni.getStorageSync('authorization')}`,
      },
      withCredentials: true
    });

    if (res.data && res.data.data) {
      userInfo.value = res.data.data;
      formData.value = {
        id: userInfo.value.id,
        nickname: userInfo.value.nickname || '',
        gender: userInfo.value.gender || '',
        birthday: userInfo.value.birthday || ''
      };

      console.log('获取到的用户信息:', userInfo.value);
      console.log('表单数据:', formData.value);
    }
  } catch (error) {
    console.error('获取用户信息失败:', error);
  }
};

// 上传头像文件（与 daily.vue 相同的方式）
const uploadAvatar = (filePath) => {
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url: baseURL + 'user/upload-avatar', // 专门的头像上传接口
      filePath: filePath,
      name: 'file',
      header: {
        'Cookie': `Authorization=${uni.getStorageSync('authorization')}`,
      },
      success: (uploadRes) => {
        console.log('头像上传响应:', uploadRes);

        if (uploadRes.statusCode === 200) {
          try {
            const result = JSON.parse(uploadRes.data);
            console.log('解析后的响应数据:', result);

            if (result.code === 0) {
              const avatarPath = result.data;
              console.log('头像上传成功，路径:', avatarPath);
              resolve(avatarPath);
            } else {
              reject(new Error(result.msg || result.message || '头像上传失败'));
            }
          } catch (e) {
            console.error('响应解析失败:', uploadRes.data);
            reject(new Error('头像上传响应解析失败'));
          }
        } else {
          reject(new Error(`头像上传失败: 状态码 ${uploadRes.statusCode}`));
        }
      },
      fail: (error) => {
        console.error('头像上传请求失败:', error);
        reject(new Error('头像上传请求失败'));
      }
    });
  });
};

// 保存设置（包含头像上传）
const saveSettings = async () => {
  try {
    console.log('开始保存用户信息:', formData.value);
    console.log('选择的头像路径:', selectedAvatarPath.value);

    uni.showLoading({
      title: '保存中...',
      mask: true
    });

    // 准备保存数据
    const saveData = {
      nickname: formData.value.nickname,
      gender: formData.value.gender,
      birthday: formData.value.birthday
    };

    // 如果有选择新头像，先上传头像
    if (selectedAvatarPath.value) {
      console.log('上传新头像...');
      const avatarPath = await uploadAvatar(selectedAvatarPath.value);
      saveData.avatar = avatarPath;
      console.log('头像上传成功，路径:', avatarPath);
    }

    console.log('最终保存数据:', saveData);

    const res = await uni.request({
      url: baseURL + 'user',
      method: 'PUT',
      header: {
        'Cookie': `Authorization=${uni.getStorageSync('authorization')}`,
        'Content-Type': 'application/json'
      },
      data: saveData,
      withCredentials: true
    });

    console.log('保存响应:', res);

    if (res.data && res.data.code === 0) {
      uni.showToast({ title: '保存成功', icon: 'success' });
      selectedAvatarPath.value = ''; // 清空选择的头像路径
      await getUserInfo(); // 重新获取用户信息
    } else {
      const errorMsg = res.data?.msg || res.data?.message || '保存失败';
      console.error('保存设置失败:', errorMsg);
      uni.showToast({ title: errorMsg, icon: 'none' });
    }
  } catch (error) {
    console.error('保存设置失败:', error);
    uni.showToast({
      title: error.message || '保存失败',
      icon: 'none',
      duration: 2000
    });
  } finally {
    uni.hideLoading();
  }
};

// 获取头像显示URL（优先显示选择的新头像）
const getAvatarUrl = () => {
  if (selectedAvatarPath.value) {
    return selectedAvatarPath.value; // 显示选择的新头像
  }
  return userInfo.value.avatar; // 显示当前头像
};

// 退出登录
const logout = async () => {
  try {
    const res = await uni.request({
      url: baseURL + 'user',
      method: 'DELETE',
      header: {
        'Cookie': `Authorization=${uni.getStorageSync('authorization')}`
      },
      withCredentials: true
    });

    if (res.data && res.data.code === 0) {
      uni.removeStorageSync('authorization');
      uni.reLaunch({ url: '/pages/login/login' });
    } else {
      uni.showToast({ title: res.data.message || '退出失败', icon: 'none' });
    }
  } catch (error) {
    console.error('退出登录失败:', error);
    uni.showToast({ title: '退出失败', icon: 'none' });
  }
};

// 头像选择相关方法
const showAvatarPicker = () => {
  uni.showActionSheet({
    itemList: ['从相册选择', '拍照'],
    success: (res) => {
      if (res.tapIndex === 0) {
        chooseAvatar();
      } else if (res.tapIndex === 1) {
        takePhoto();
      }
    }
  });
};

const chooseAvatar = async () => {
  try {
    const res = await uni.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album']
    });

    if (res.tempFilePaths.length > 0) {
      selectedAvatarPath.value = res.tempFilePaths[0];
      console.log('选择头像成功:', selectedAvatarPath.value);
      uni.showToast({ title: '头像已选择，点击保存设置生效', icon: 'none' });
    }
  } catch (error) {
    console.error('选择头像失败:', error);
    uni.showToast({ title: '选择头像失败', icon: 'none' });
  }
};

const takePhoto = async () => {
  try {
    const res = await uni.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['camera']
    });

    if (res.tempFilePaths.length > 0) {
      selectedAvatarPath.value = res.tempFilePaths[0];
      console.log('拍照成功:', selectedAvatarPath.value);
      uni.showToast({ title: '头像已选择，点击保存设置生效', icon: 'none' });
    }
  } catch (error) {
    console.error('拍照失败:', error);
    uni.showToast({ title: '拍照失败', icon: 'none' });
  }
};

const onGenderChange = (e) => {
  formData.value.gender = genderOptions[e.detail.value];
};

const onBirthdayChange = (e) => {
  formData.value.birthday = e.detail.value;
};

onMounted(() => {
  getUserInfo();
});
</script>

<style>
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.user-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.avatar-container {
  position: relative;
  margin-right: 30rpx;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
}

.edit-avatar-btn {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  font-size: 24rpx;
  height: 40rpx;
  line-height: 40rpx;
  border-radius: 0 0 60rpx 60rpx;
}

.user-info {
  flex: 1;
}

.nickname {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.username {
  font-size: 24rpx;
  color: #999;
}

.settings-form {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.form-item {
  margin-bottom: 30rpx;
}

.label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}

.input {
  height: 80rpx;
  border: 1rpx solid #eee;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}

.picker {
  height: 80rpx;
  border: 1rpx solid #eee;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  line-height: 80rpx;
}

.save-btn {
  background-color: #07c160;
  color: #fff;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  margin-top: 20rpx;
}

.logout-btn {
  background-color: #ff453a;
  color: #fff;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  margin-top: 20rpx;
}

.avatar-popup {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 16rpx 16rpx 0 0;
}

.avatar-popup button {
  margin-bottom: 20rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  border-radius: 8rpx;
  background-color: #f5f5f5;
  color: #333;
}

.avatar-popup button:last-child {
  margin-bottom: 0;
}
</style>