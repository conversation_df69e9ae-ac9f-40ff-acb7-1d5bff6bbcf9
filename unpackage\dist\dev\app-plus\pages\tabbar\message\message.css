
.message-container {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f8f8f8;
}
.header {
		height: 2.8125rem;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #ffffff;
		border-bottom: 0.03125rem solid #eeeeee;
		position: -webkit-sticky;
		position: sticky;
		top: 0;
		z-index: 100;
}
.title {
		font-size: 1.125rem;
		font-weight: bold;
		color: #333333;
}
.message-list {
		flex: 1;
		padding: 0.625rem;
}
.empty-state {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		height: 18.75rem;
}
.empty-image {
		width: 6.25rem;
		height: 6.25rem;
		margin-bottom: 0.625rem;
}
.empty-text {
		font-size: 0.875rem;
		color: #999999;
}
.message-item {
		display: flex;
		padding: 0.9375rem;
		background-color: #ffffff;
		border-radius: 0.375rem;
		margin-bottom: 0.625rem;
		box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.message-item.unread {
		background-color: #f0f9ff;
}
.avatar-container {
		position: relative;
		margin-right: 0.625rem;
}
.avatar {
		width: 2.5rem;
		height: 2.5rem;
		border-radius: 50%;
}
.unread-dot {
		position: absolute;
		top: 0;
		right: 0;
		width: 0.625rem;
		height: 0.625rem;
		border-radius: 50%;
		background-color: #ff4d4f;
}
.message-content {
		flex: 1;
}
.message-header {
		display: flex;
		justify-content: space-between;
		margin-bottom: 0.3125rem;
}
.sender {
		font-size: 0.9375rem;
		font-weight: bold;
		color: #333333;
}
.time {
		font-size: 0.75rem;
		color: #999999;
}
.preview {
		font-size: 0.875rem;
		color: #666666;
		line-height: 1.5;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
}
.tabs {
		display: flex;
		background-color: #ffffff;
		padding: 0 0.625rem;
		border-bottom: 0.03125rem solid #eeeeee;
		overflow-x: auto;
		white-space: nowrap;
}
.tab-item {
		padding: 0.625rem 0.9375rem;
		position: relative;
}
.tab-item uni-text {
		font-size: 0.875rem;
		color: #666666;
}
.tab-item.active uni-text {
		color: #1890ff;
		font-weight: 500;
}
.tab-item.active::after {
		content: '';
		position: absolute;
		bottom: 0;
		left: 50%;
		transform: translateX(-50%);
		width: 1.25rem;
		height: 0.125rem;
		background-color: #1890ff;
		border-radius: 0.0625rem;
}
.message-detail {
		width: 18.75rem;
		background-color: #ffffff;
		border-radius: 0.375rem;
		overflow: hidden;
}
.detail-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0.9375rem;
		border-bottom: 0.03125rem solid #eeeeee;
}
.detail-title {
		font-size: 1rem;
		font-weight: bold;
		color: #333333;
}
.close-btn {
		font-size: 1.25rem;
		color: #999999;
		padding: 0 0.625rem;
}
.detail-content {
		padding: 0.9375rem;
		max-height: 18.75rem;
		overflow-y: auto;
}
.detail-info {
		display: flex;
		justify-content: space-between;
		margin-bottom: 0.625rem;
}
.detail-sender {
		font-size: 0.9375rem;
		font-weight: bold;
		color: #333333;
}
.detail-time {
		font-size: 0.75rem;
		color: #999999;
}
.detail-text {
		font-size: 0.875rem;
		color: #333333;
		line-height: 1.6;
}
.detail-actions {
		display: flex;
		justify-content: space-between;
		padding: 0.9375rem;
		border-top: 0.03125rem solid #eeeeee;
}
.action-btn {
		width: 45%;
		height: 2.5rem;
		line-height: 2.5rem;
		text-align: center;
		border-radius: 1.25rem;
		font-size: 0.875rem;
}
.delete {
		background-color: #ff4d4f;
		color: #ffffff;
}
.mark {
		background-color: #1890ff;
		color: #ffffff;
}
