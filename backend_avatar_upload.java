// 在你的 UserController 中添加这个方法

/**
 * 上传头像文件
 */
@PostMapping("/upload-avatar")
@UserLogin
@CrossOrigin
public CommonResult<String> uploadAvatar(@RequestParam("file") MultipartFile file, User userInfo) {
    try {
        System.out.println("收到头像上传请求，用户ID: " + userInfo.getId());
        System.out.println("文件名: " + file.getOriginalFilename());
        System.out.println("文件大小: " + file.getSize());

        // 1. 文件校验
        if (file.isEmpty()) {
            return CommonResult.webError(-1, "文件不能为空");
        }

        // 2. 文件类型校验
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || !isImageFile(originalFilename)) {
            return CommonResult.webError(-1, "只支持图片格式文件");
        }

        // 3. 文件大小校验（限制5MB）
        if (file.getSize() > 5 * 1024 * 1024) {
            return CommonResult.webError(-1, "文件大小不能超过5MB");
        }

        // 4. 生成文件路径：avatar/用户ID/时间戳.扩展名
        String fileExtension = getFileExtension(originalFilename);
        String fileName = userInfo.getId() + "/" + System.currentTimeMillis() + "." + fileExtension;

        // 5. 上传到OSS
        boolean uploadResult = aliyunService.uploadAvatarFile(file, fileName);

        if (uploadResult) {
            // 返回包含 avatar/ 前缀的文件路径
            String filePath = "avatar/" + fileName;
            System.out.println("头像上传成功，返回路径: " + filePath);
            return CommonResult.webSuccess("上传成功", filePath);
        } else {
            return CommonResult.webError(-1, "上传失败");
        }
    } catch (Exception e) {
        e.printStackTrace();
        return CommonResult.webError(-1, "上传失败: " + e.getMessage());
    }
}

/**
 * 检查是否为图片文件
 */
private boolean isImageFile(String filename) {
    String extension = getFileExtension(filename).toLowerCase();
    return extension.equals("jpg") || extension.equals("jpeg") || 
           extension.equals("png") || extension.equals("gif") || 
           extension.equals("bmp") || extension.equals("webp");
}

/**
 * 获取文件扩展名
 */
private String getFileExtension(String filename) {
    if (filename == null || filename.lastIndexOf(".") == -1) {
        return "jpg"; // 默认扩展名
    }
    return filename.substring(filename.lastIndexOf(".") + 1);
}

// 同时修改你现有的 editUserInfo 方法，移除 base64 处理部分：

/**
 * 修改用户信息
 */
@PutMapping
@UserLogin
@CrossOrigin
public CommonResult<Object> editUserInfo(@RequestBody UserInfoReq requestBody, User userInfo) {
    try {
        System.out.println("=== 修改用户信息 ===");
        System.out.println("用户ID: " + userInfo.getId());
        System.out.println("请求数据: " + requestBody);
        
        userService.editUserInfo(userInfo.getId(),
            requestBody.getNickname(),
            requestBody.getAvatar(), // 直接使用传入的头像路径
            requestBody.getGender(),
            requestBody.getBirthday());
            
        return CommonResult.webSuccess("修改成功");
    } catch (Exception e) {
        System.err.println("用户信息更新失败: " + e.getMessage());
        e.printStackTrace();
        return CommonResult.webError(-1, "用户信息更新失败：" + e.getMessage());
    }
}

// UserInfoReq 类需要确保有这些字段：
/*
public class UserInfoReq {
    private String nickname;
    private String avatar;  // 头像路径，不再是 base64
    private String gender;
    private String birthday;
    
    // getters and setters...
}
*/
