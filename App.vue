<script>
	export default {
		globalData: {
			isLogin: false
		},
		onLaunch: function() {
			console.log('App Launch')
			// 检查用户是否已登录
			const authorization = uni.getStorageSync('authorization');
			console.log('检查到的authorization:', authorization);

			if (authorization) {
				// 用户已登录
				console.log('用户已登录，保持在当前页面');
				this.globalData.isLogin = true;
			} else {
				// 用户未登录，跳转到登录页面
				console.log('用户未登录，跳转到登录页');
				this.globalData.isLogin = false;
				// 延迟跳转，确保应用完全启动
				setTimeout(() => {
					uni.reLaunch({
						url: '/pages/login/login'
					});
				}, 100);
			}
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		},
		methods: {
			// 全局检查登录状态方法
			checkLogin() {
				const authorization = uni.getStorageSync('authorization');
				if (!authorization) {
					uni.showToast({
						title: '请先登录',
						icon: 'none'
					});
					uni.navigateTo({
						url: '/pages/login/login'
					});
					return false;
				}
				return true;
			},
			// 退出登录方法
			logout() {
				uni.removeStorageSync('authorization');
				this.globalData.isLogin = false;
				uni.reLaunch({
					url: '/pages/login/login'
				});
			}
		}
	}
</script>

<style>
	/*每个页面公共css */
	@import url('./static/iconfont.css');
</style>
