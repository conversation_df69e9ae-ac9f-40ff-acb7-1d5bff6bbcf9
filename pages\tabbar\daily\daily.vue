<template>
  <view class="container">
    <!-- 添加日记表单 -->
    <view class="form-container">
      <view class="form-header">
        <text class="form-title">新建日记</text>
      </view>
      <input class="input-field" v-model="formData.title" placeholder="请输入标题" />
      <textarea class="textarea-field" v-model="formData.content" placeholder="写下你的想法..." />
      
      <!-- 图片选择区域 -->
      <view class="image-upload">
        <image v-if="selectedImagePath || formData.image" :src="getImageUrl(selectedImagePath || formData.image)" class="preview-image" mode="aspectFill" @click="previewImage(getImageUrl(selectedImagePath || formData.image))" />
        <view v-else class="upload-placeholder" @click="chooseImage">
          <text class="iconfont icon-camera"></text>
          <text>点击选择图片</text>
        </view>
      </view>
      
      <view class="form-actions">
        <button class="submit-btn" @click="addDaily">发布</button>
        <button class="cancel-btn" @click="resetForm">重置</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { reactive, ref } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import request from '../../../api/request';

const baseURL = request.BASE_URL;
const app = getApp();
const authorization = uni.getStorageSync('authorization');

// 响应式数据
const formData = reactive({
  userId: null,
  title: '',
  content: '',
  image: ''
});

// 存储选择的图片文件路径
const selectedImagePath = ref('');

// 图片选择方法（只选择，不上传）
const chooseImage = async () => {
  try {
    const chooseRes = await uni.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      maxSize: 5 * 1024 * 1024
    });

    if (!chooseRes.tempFilePaths || chooseRes.tempFilePaths.length === 0) {
      throw new Error('未选择图片');
    }

    const tempFilePath = chooseRes.tempFilePaths[0];
    console.log('选择的图片路径:', tempFilePath);

    // 只保存图片路径，不立即上传
    selectedImagePath.value = tempFilePath;
    formData.image = tempFilePath; // 临时显示用

    uni.showToast({
      title: '图片选择成功',
      icon: 'success'
    });

  } catch (error) {
    console.error('图片选择失败:', error);
    uni.showToast({
      title: error.message || '图片选择失败',
      icon: 'none',
      duration: 3000
    });
  }
};

// 上传图片方法
const uploadImage = async (filePath) => {
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url: baseURL + 'daily/upload',
      filePath: filePath,
      name: 'file',
      header: {
        'Cookie': `Authorization=${authorization}`,
      },
      success: (uploadRes) => {
        console.log('上传响应:', uploadRes);

        if (uploadRes.statusCode === 200) {
          try {
            const result = JSON.parse(uploadRes.data);
            console.log('解析后的响应数据:', result);

            if (result.code === 0) {
              const uploadedFileName = result.data;
              const imagePath = `daily/${uploadedFileName}`;
              console.log('上传成功，图片路径:', imagePath);
              resolve(imagePath);
            } else {
              reject(new Error(result.message || '上传失败'));
            }
          } catch (e) {
            console.error('响应解析失败:', uploadRes.data);
            reject(new Error('上传响应解析失败'));
          }
        } else {
          reject(new Error(`上传失败: 状态码 ${uploadRes.statusCode}`));
        }
      },
      fail: (error) => {
        console.error('上传请求失败:', error);
        reject(new Error('上传请求失败'));
      }
    });
  });
};

// 图片预览方法
const previewImage = (url) => {
  uni.previewImage({
    urls: [url]
  });
};

// 构建图片URL
const getImageUrl = (imageName) => {
  if (!imageName) return '';
  if (imageName.startsWith('http')) return imageName;
  if (imageName.startsWith('file://')) return imageName; // 本地临时文件

  // 使用后端的文件访问接口构建URL
  return `${baseURL}file/${imageName}`;
};

// 添加日记方法（统一处理上传和添加）
const addDaily = async () => {
  if (!formData.title.trim() || !formData.content.trim()) {
    uni.showToast({
      title: '请填写标题和内容',
      icon: 'none'
    });
    return;
  }

  try {
    // 确保设置用户ID
    if (!app.globalData.userId) {
      throw new Error('用户未登录，请重新登录');
    }

    uni.showLoading({
      title: '发布中...',
      mask: true
    });

    // 准备发布数据
    const publishData = {
      userId: app.globalData.userId,
      title: formData.title.trim(),
      content: formData.content.trim(),
      image: ''
    };

    // 如果有选择图片，先上传图片
    if (selectedImagePath.value) {
      console.log('开始上传图片:', selectedImagePath.value);
      try {
        const uploadedImagePath = await uploadImage(selectedImagePath.value);
        publishData.image = uploadedImagePath;
        console.log('图片上传成功:', uploadedImagePath);
      } catch (uploadError) {
        console.error('图片上传失败:', uploadError);
        throw new Error('图片上传失败: ' + uploadError.message);
      }
    }

    console.log('发布日记数据:', publishData);

    // 发布日记
    const res = await uni.request({
      url: baseURL + 'daily',
      method: 'POST',
      header: {
        'Cookie': `Authorization=${authorization}`,
        'Content-Type': 'application/json'
      },
      data: publishData
    });

    console.log('发布日记响应:', res);

    if (res.statusCode !== 200) {
      throw new Error(`请求失败，状态码: ${res.statusCode}`);
    }

    if (res.data && res.data.code === 0) {
      uni.showToast({
        title: '发布成功',
        icon: 'success'
      });
      resetForm();
    } else {
      throw new Error(res.data?.message || '发布失败');
    }
  } catch (error) {
    console.error('发布失败:', error);
    uni.showToast({
      title: error.message || '发布失败',
      icon: 'none',
      duration: 3000
    });
  } finally {
    uni.hideLoading();
  }
};

// 重置表单
const resetForm = () => {
  formData.userId = null;
  formData.title = '';
  formData.content = '';
  formData.image = '';
  selectedImagePath.value = '';
};

// 生命周期钩子
onLoad(() => {
  if (!app.checkLogin()) return;

  // 调试用户信息
  console.log('当前用户ID:', app.globalData.userId);
  console.log('当前登录状态:', app.globalData.isLogin);
  console.log('Authorization:', authorization);
});
</script>

<style>
.container {
  padding: 20rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}

.form-container {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.form-header {
  margin-bottom: 30rpx;
  text-align: center;
}

.form-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.input-field {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  margin-bottom: 20rpx;
  border: 2rpx solid #eee;
  border-radius: 8rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.textarea-field {
  width: 100%;
  height: 200rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border: 2rpx solid #eee;
  border-radius: 8rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.image-upload {
  margin: 20rpx 0;
}

.preview-image {
  width: 100%;
  height: 400rpx;
  border-radius: 8rpx;
}

.upload-placeholder {
  width: 100%;
  height: 200rpx;
  border: 2rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #999;
}

.icon-camera {
  font-size: 48rpx;
  margin-bottom: 10rpx;
}

.form-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 30rpx;
}

.submit-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #4CAF50;
  color: white;
  border-radius: 40rpx;
  font-size: 28rpx;
  text-align: center;
  border: none;
}

.cancel-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #f5f5f5;
  color: #666;
  border-radius: 40rpx;
  font-size: 28rpx;
  text-align: center;
  border: none;
}
</style>

