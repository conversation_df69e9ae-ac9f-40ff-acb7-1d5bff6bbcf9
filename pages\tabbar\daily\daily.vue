<template>
  <view class="container">
    <!-- 添加日记表单 -->
    <view class="form-container">
      <view class="form-header">
        <text class="form-title">新建日记</text>
      </view>
      <input class="input-field" v-model="formData.title" placeholder="请输入标题" />
      <textarea class="textarea-field" v-model="formData.content" placeholder="写下你的想法..." />
      
      <!-- 图片上传区域 -->
      <view class="image-upload">
        <image v-if="formData.image" :src="getImageUrl(formData.image)" class="preview-image" mode="aspectFill" @click="previewImage(getImageUrl(formData.image))" />
        <view v-else class="upload-placeholder" @click="chooseImage">
          <text class="iconfont icon-camera"></text>
          <text>点击添加图片</text>
        </view>
      </view>
      
      <view class="form-actions">
        <button class="submit-btn" @click="addDaily">发布</button>
        <button class="cancel-btn" @click="resetForm">重置</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { reactive } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import request from '../../../api/request';

const baseURL = request.BASE_URL;
const app = getApp();
const authorization = uni.getStorageSync('authorization');

// 响应式数据
const formData = reactive({
  userId: null,
  title: '',
  content: '',
  image: ''
});

// 图片上传方法
const chooseImage = async () => {
  try {
    const chooseRes = await uni.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      maxSize: 5 * 1024 * 1024
    });
    
    if (!chooseRes.tempFilePaths || chooseRes.tempFilePaths.length === 0) {
      throw new Error('未选择图片');
    }
    
    const tempFilePath = chooseRes.tempFilePaths[0];
    
    uni.showLoading({
      title: '上传中...',
      mask: true
    });
    
    const uploadRes = await uni.uploadFile({
      url: baseURL + 'daily/upload',
      filePath: tempFilePath,
      name: 'file',
      header: {
        'Authorization': authorization
      }
    });
    
    uni.hideLoading();
    
    if (uploadRes.statusCode !== 200) {
      throw new Error(`上传失败: 状态码 ${uploadRes.statusCode}`);
    }
    
    let result;
    try {
      result = JSON.parse(uploadRes.data);
    } catch (e) {
      throw new Error('上传响应解析失败');
    }
    
    if (result.code === 0) {
      // 存储上传成功后的文件名
      const fileName = tempFilePath.split('/').pop();
      formData.image = fileName;
      
      uni.showToast({
        title: '上传成功',
        icon: 'success'
      });
    } else {
      throw new Error(result.message || '上传失败');
    }
  } catch (error) {
    uni.hideLoading();
    console.error('图片上传失败:', error);
    uni.showToast({
      title: error.message || '图片上传失败',
      icon: 'none',
      duration: 3000
    });
  }
};

// 图片预览方法
const previewImage = (url) => {
  uni.previewImage({
    urls: [url]
  });
};

// 构建图片URL
const getImageUrl = (imageName) => {
  if (!imageName) return '';
  if (imageName.startsWith('http')) return imageName;
  // 根据你的后端OSS配置构建完整URL
  return `${baseURL}file/${imageName}`;
};

// 添加日记方法
const addDaily = async () => {
  if (!formData.title.trim() || !formData.content.trim()) {
    uni.showToast({
      title: '请填写标题和内容',
      icon: 'none'
    });
    return;
  }
  
  try {
    formData.userId = app.globalData.userId;
    
    const res = await uni.request({
      url: baseURL + 'daily',
      method: 'POST',
      header: {
        'Authorization': authorization,
        'Content-Type': 'application/json'
      },
      data: formData
    });
    
    if (res.statusCode !== 200) {
      throw new Error(`请求失败，状态码: ${res.statusCode}`);
    }
    
    if (res.data && res.data.code === 0) {
      uni.showToast({
        title: '发布成功',
        icon: 'success'
      });
      resetForm();
    } else {
      throw new Error(res.data?.message || '发布失败');
    }
  } catch (error) {
    console.error('添加日记失败:', error);
    uni.showToast({
      title: error.message || '发布失败',
      icon: 'none',
      duration: 3000
    });
  }
};

// 重置表单
const resetForm = () => {
  formData.userId = null;
  formData.title = '';
  formData.content = '';
  formData.image = '';
};

// 生命周期钩子
onLoad(() => {
  if (!app.checkLogin()) return;
});
</script>

<style>
.container {
  padding: 20rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}

.form-container {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.form-header {
  margin-bottom: 30rpx;
  text-align: center;
}

.form-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.input-field {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  margin-bottom: 20rpx;
  border: 2rpx solid #eee;
  border-radius: 8rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.textarea-field {
  width: 100%;
  height: 200rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border: 2rpx solid #eee;
  border-radius: 8rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.image-upload {
  margin: 20rpx 0;
}

.preview-image {
  width: 100%;
  height: 400rpx;
  border-radius: 8rpx;
}

.upload-placeholder {
  width: 100%;
  height: 200rpx;
  border: 2rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #999;
}

.icon-camera {
  font-size: 48rpx;
  margin-bottom: 10rpx;
}

.form-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 30rpx;
}

.submit-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #4CAF50;
  color: white;
  border-radius: 40rpx;
  font-size: 28rpx;
  text-align: center;
  border: none;
}

.cancel-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #f5f5f5;
  color: #666;
  border-radius: 40rpx;
  font-size: 28rpx;
  text-align: center;
  border: none;
}
</style>

